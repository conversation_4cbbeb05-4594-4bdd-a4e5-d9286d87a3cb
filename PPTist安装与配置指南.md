# PPTist安装与配置指南

## 项目简介
PPTist是一个基于Vue 3.x和TypeScript开发的在线演示文稿应用，复制了Microsoft PowerPoint的大部分常用功能，支持在线编辑和演示PPT。

## 主要特性
- **易于开发**: 基于Vue 3.x和TypeScript，不依赖UI组件库
- **用户友好**: 提供右键菜单、快捷键、编辑细节优化
- **功能丰富**: 支持PowerPoint常用元素和功能，支持AI PPT生成
- **多格式导出**: 支持PPTX、JSON、图片、PDF等格式导出

## 安装步骤

### 1. 项目下载
```bash
# 方法1: Git克隆（如果网络允许）
git clone https://github.com/pipipi-pikachu/PPTist.git .

# 方法2: 下载ZIP文件
# 使用PowerShell下载
Invoke-WebRequest -Uri "https://github.com/pipipi-pikachu/PPTist/archive/refs/heads/master.zip" -OutFile "pptist.zip"

# 解压文件
Expand-Archive -Path "pptist.zip" -DestinationPath "." -Force
```

### 2. 依赖安装
```bash
# 安装项目依赖
npm install
```

### 3. 启动开发服务器
```bash
# 启动开发服务器
npm run dev
```

### 4. 访问应用
- 本地访问地址: http://127.0.0.1:5173/
- 开发服务器启动后会自动显示访问地址

## 一键启动脚本

### Windows批处理文件 (start-pptist.bat)
```batch
@echo off
echo Starting PPTist...
echo.

REM Change to the PPTist directory
cd /d "%~dp0"

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    echo.
)

REM Start the development server
echo Starting PPTist development server...
echo PPTist will be available at: http://127.0.0.1:5173/
echo.
echo Press Ctrl+C to stop the server
echo.

npm run dev

pause
```

### PowerShell脚本 (start-pptist.ps1)
```powershell
# PPTist Startup Script
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

Write-Host "Starting PPTist..." -ForegroundColor Green
Write-Host ""

# Change to the script directory
Set-Location $PSScriptRoot

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if node_modules exists
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install
    Write-Host ""
}

# Start the development server
Write-Host "Starting PPTist development server..." -ForegroundColor Green
Write-Host "PPTist will be available at: http://127.0.0.1:5173/" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Wait a moment then open browser
Start-Job -ScriptBlock {
    Start-Sleep 3
    Start-Process "http://127.0.0.1:5173/"
} | Out-Null

npm run dev

Read-Host "Press Enter to exit"
```

## 使用方法

### 启动应用
1. **双击批处理文件**: 直接双击 `start-pptist.bat`
2. **PowerShell脚本**: 右键点击 `start-pptist.ps1` → "Run with PowerShell"
3. **命令行启动**: 在项目目录下运行 `npm run dev`

### PowerShell执行策略问题解决
如果遇到PowerShell执行策略错误，使用以下命令：
```powershell
# 方法1: 使用.\ 前缀
.\start-pptist.ps1

# 方法2: 绕过执行策略
PowerShell -ExecutionPolicy Bypass -File "start-pptist.ps1"

# 方法3: 使用批处理文件（推荐）
start-pptist.bat
```

## 项目结构
```
PPTist/
├── src/                 # 源代码目录
├── public/              # 静态资源
├── doc/                 # 文档
├── package.json         # 项目配置
├── vite.config.ts       # Vite配置
├── start-pptist.bat     # Windows启动脚本
└── start-pptist.ps1     # PowerShell启动脚本
```

## 开发命令
- `npm run dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm run preview` - 预览生产构建
- `npm run type-check` - TypeScript类型检查
- `npm run lint` - 代码检查

## 技术栈
- **前端框架**: Vue 3.5.17
- **开发语言**: TypeScript
- **构建工具**: Vite 5.3.5
- **状态管理**: Pinia
- **图表库**: ECharts
- **富文本编辑**: ProseMirror

## 注意事项
1. 确保已安装Node.js (推荐版本18+)
2. 网络问题可能影响依赖安装，可使用国内镜像
3. 开发服务器默认端口5173，如被占用会自动选择其他端口
4. 首次安装需要下载依赖包，可能需要几分钟时间

## 故障排除
- **端口被占用**: 修改vite.config.ts中的端口配置
- **依赖安装失败**: 尝试删除node_modules文件夹重新安装
- **启动失败**: 检查Node.js版本是否符合要求
- **网络问题**: 配置npm镜像源或使用VPN
